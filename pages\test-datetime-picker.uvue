<template>
	<!-- #ifdef APP -->
	<scroll-view class="container">
	<!-- #endif -->
		<view class="page-container">
			<view class="header">
				<text class="title">日期时间选择器测试</text>
			</view>
			
			<view class="test-section">
				<view class="test-item" v-for="(test, index) in testCases" :key="index">
					<view class="test-label">
						<text class="label-text">{{ test.label }}</text>
						<text class="mode-text">模式: {{ test.mode }}</text>
					</view>
					<view class="test-value" @click="showPicker(test.mode, index)">
						<text class="value-text">{{ test.displayValue || '点击选择' }}</text>
					</view>
					<view v-if="test.mapValue" class="map-display">
						<text class="map-title">Map值:</text>
						<text class="map-content">{{ formatMapValue(test.mapValue) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 日期时间选择器 -->
		<main-datetime-picker 
			ref="datetimePicker"
			:mode="currentMode"
			:title="currentTitle"
			@confirm="onDateTimeConfirm"
			@cancel="onDateTimeCancel"
		/>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	import MainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'
	
	type TestCase = {
		label: string
		mode: string
		displayValue: string
		mapValue: Map<string, any> | null
	}
	
	export default {
		name: "TestDatetimePicker",
		components: {
			MainDatetimePicker
		},
		data() {
			return {
				currentMode: 'datetime' as string,
				currentTitle: '选择时间' as string,
				currentTestIndex: -1 as number,
				testCases: [
					{
						label: '时间范围',
						mode: 'time-range',
						displayValue: '',
						mapValue: null
					},
					{
						label: '月份',
						mode: 'month',
						displayValue: '',
						mapValue: null
					},
					{
						label: '日期',
						mode: 'day',
						displayValue: '',
						mapValue: null
					},
					{
						label: '时间',
						mode: 'time',
						displayValue: '',
						mapValue: null
					},
					{
						label: '时分秒',
						mode: 'hour-minute-second',
						displayValue: '',
						mapValue: null
					},
					{
						label: '年份',
						mode: 'year',
						displayValue: '',
						mapValue: null
					},
					{
						label: '年月',
						mode: 'year-month',
						displayValue: '',
						mapValue: null
					}
				] as TestCase[]
			}
		},
		methods: {
			showPicker(mode: string, index: number) {
				this.currentMode = mode
				this.currentTestIndex = index
				this.currentTitle = this.testCases[index].label
				
				const picker = this.$refs['datetimePicker'] as ComponentPublicInstance
				picker.$callMethod('show')
			},
			
			onDateTimeConfirm(result: any) {
				console.log('选择结果:', result)
				
				if (this.currentTestIndex >= 0) {
					this.testCases[this.currentTestIndex].displayValue = result.formatted
					this.testCases[this.currentTestIndex].mapValue = result.value
				}
			},
			
			onDateTimeCancel() {
				console.log('取消选择')
			},
			
			formatMapValue(mapValue: Map<string, any> | Map<string, any>[] | null): string {
				if (!mapValue) return ''
				
				if (Array.isArray(mapValue)) {
					// 处理区间值
					return mapValue.map(map => this.formatSingleMap(map)).join(' | ')
				} else {
					// 处理单个值
					return this.formatSingleMap(mapValue)
				}
			},
			
			formatSingleMap(map: Map<string, any>): string {
				const parts: string[] = []
				
				if (map.has('year')) parts.push(`year:${map.get('year')}`)
				if (map.has('month')) parts.push(`month:${map.get('month')}`)
				if (map.has('day')) parts.push(`day:${map.get('day')}`)
				if (map.has('hour')) parts.push(`hour:${map.get('hour')}`)
				if (map.has('minute')) parts.push(`minute:${map.get('minute')}`)
				if (map.has('second')) parts.push(`second:${map.get('second')}`)
				
				return `{${parts.join(', ')}}`
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}
	
	.page-container {
		padding: 40rpx;
	}
	
	.header {
		margin-bottom: 60rpx;
		text-align: center;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.test-section {
		display: flex;
		flex-direction: column;
	}
	
	.test-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
	
	.test-label {
		display: flex;
		flex-direction: column;
		margin-bottom: 20rpx;
	}
	
	.label-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.mode-text {
		font-size: 24rpx;
		color: #666;
	}
	
	.test-value {
		background-color: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 8rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
	}
	
	.value-text {
		font-size: 28rpx;
		color: #495057;
	}
	
	.map-display {
		background-color: #e3f2fd;
		border-radius: 8rpx;
		padding: 20rpx;
	}
	
	.map-title {
		font-size: 24rpx;
		font-weight: bold;
		color: #1976d2;
		margin-bottom: 8rpx;
	}
	
	.map-content {
		font-size: 22rpx;
		color: #424242;
		word-break: break-all;
	}
</style>

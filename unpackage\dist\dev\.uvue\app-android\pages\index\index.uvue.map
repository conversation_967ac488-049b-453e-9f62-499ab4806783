{"version": 3, "sources": ["pages/index/index.uvue"], "names": [], "mappings": "AA+BC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,mBAAkB,MAAO,yDAAwD,CAAA;AACxF,OAAO,kBAAiB,MAAO,wDAAuD,CAAA;AAEtF,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,UAAU,EAAC;QACV,mBAAmB;QACnB,kBAAiB;KACjB;IACD,IAAI;QACH,OAAO;YACN,YAAW;YACX,YAAY,EAAE,MAAK,IAAK,MAAM;YAC9B,aAAa,EAAE,QAAO,IAAK,MAAM;YACjC,WAAW,EAAE,KAAI,IAAK,OAAO;YAC7B,YAAY,EAAE,EAAC,IAAK,eAAe,EAAE;YAClC,UAAU,EAAE;gBACd;oBACC,GAAG,EAAE,UAAU;oBACf,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE;oBACT,MAAM,EAAC,IAAI;oBACX,KAAK,EAAC;wBACL,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,EAAE;wBACb,WAAW,EAAE,QAAQ;wBACrB,GAAG,EAAC,KAAK;wBACT,SAAS,EAAE,OAAM;qBAClB;iBACA;gBACD;oBACC,GAAG,EAAE,UAAU;oBACf,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE;oBACT,MAAM,EAAC,KAAK;oBACZ,KAAK,EAAC;wBACL,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,EAAE;wBACb,WAAW,EAAE,OAAO;wBACpB,GAAG,EAAC,UAAU;wBACd,SAAS,EAAE,QAAO;qBACnB;iBACA;gBACD;oBACC,GAAG,EAAE,OAAO;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,EAAE;oBACT,MAAM,EAAC,IAAI;oBACX,KAAK,EAAC;wBACL,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,EAAE;wBACb,WAAW,EAAE,OAAO;wBACpB,GAAG,EAAC,EAAC;qBACN;iBAEA;gBACD;oBACI,GAAG,EAAE,gBAAgB;oBACrB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,KAAK;oBACb,KAAK,EAAE;wBACH,SAAS,EAAE,QAAQ;wBACnB,KAAK,EAAE,WAAU;qBACrB;iBACH;gBACD;oBACI,GAAG,EAAE,QAAQ;oBACb,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,KAAK,EAAE,CAAC;wBACR,KAAK,EAAE,GAAG;wBAChB,MAAM,EAAC,CAAA;qBACL;iBACH;gBACD;oBACI,GAAG,EAAE,WAAW;oBAChB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,KAAK,EAAE,CAAC;wBACR,KAAK,EAAE,EAAE;wBACf,MAAM,EAAE,CAAC;wBACT,MAAM,EAAE,GAAG;wBACX,KAAK,EAAE,OAAM;qBACX;iBACH;gBACD;oBACI,GAAG,EAAE,YAAY;oBACjB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,KAAK;oBACb,KAAK,EAAE;wBACH,SAAS,EAAE,KAAK;wBAChB,KAAK,EAAE,YAAW;qBACtB;iBACH;gBACD;oBACI,GAAG,EAAE,iBAAiB;oBACtB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,sBAAsB;oBAC7B,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,SAAS,EAAE,MAAM;wBACjB,KAAK,EAAE,cAAa;qBACxB;iBACH;gBACD;oBACI,GAAG,EAAE,gBAAgB;oBACrB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,KAAK,EAAE,YAAW;qBACtB;iBACH;gBACD;oBACI,GAAG,EAAE,MAAM;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,EAAE;oBACT,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,SAAS,EAAE,QAAQ;wBACnB,aAAa,EAAE,OAAO;wBACtB,KAAK,EAAE,UAAU;wBACjB,SAAS,EAAE;4BACP,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAC;4BAClC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAC;4BACnC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAC;4BACpC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAC;4BACnC,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAA;yBACtC;qBACJ;iBACH;gBACD;oBACI,GAAG,EAAE,OAAO;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,SAAS,EAAE,KAAK;wBAChB,aAAa,EAAE,OAAO;wBACtB,KAAK,EAAE,UAAU;wBACjB,SAAS,EAAE;4BACP,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAC;4BAC5B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAC;4BAC5B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAC;4BAC5B,EAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAA;yBAChC;qBACJ;iBACH;gBACD;oBACI,GAAG,EAAE,OAAO;oBACZ,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;oBACV,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE;wBACH,SAAS,EAAE,OAAO;wBAClB,aAAa,EAAE,OAAO;wBACtB,KAAK,EAAE,QAAQ;wBACf,SAAS,EAAE;4BACP,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAC;4BAC9B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAC;4BAC9B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAC;4BAC9B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAC;4BAC9B,EAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAA;yBACjC;qBACJ;iBACJ;aACD,IAAK,aAAa,EAAC;SACpB,CAAA;IACD,CAAC;IACD,OAAO,EAAE;QACR,QAAQ;YACP,iCAAgC;YAChC,+BAA8B;YAC9B,GAAG,CAAC,UAAU,CAAC;gBACd,GAAG,EAAC,oCAAmC;aACvC,CAAA,CAAA;QACF,CAAC;QACD,OAAO;YACN,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAA,IAAK,uBAAuB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;YAE7E,kBAAiB;YACjB,+BAA8B;YAC9B,mBAAkB;YAClB,2BAA0B;YAC1B,6BAA4B;YAC5B,uBAAsB;YACtB,2BAA0B;YAC1B,6BAA4B;YAC5B,2BAA0B;YAC1B,MAAK;YACL,KAAI;YACJ,KAAI;QACL,CAAC;QACD,eAAe;YACd,wBAAuB;YACvB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAA,IAAK,uBAAuB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QAC1E,CAAC;QAED,QAAQ;YACP,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAA,gCAAA,CAAA,CAAA;QACrB,CAAC;QAED,SAAS,CAAC,MAAK,EAAI,aAAa;YAC/B,OAAO,CAAC,GAAG,CAAC,MAAM,EAAA,gCAAA,CAAA,CAAA;YAClB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,EAAA,gCAAA,CAAA,CAAA;YACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAA,gCAAA,CAAA,CAAA;QACrC,CAAC;QAED,YAAW;QACX,kBAAkB;YACjB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAA,IAAK,uBAAuB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QAC7E,CAAC;QAED,cAAa;QACb,gBAAgB;YACf,OAAO,CAAC,GAAG,CAAC,YAAY,EAAA,gCAAA,CAAA,CAAA;QACzB,CAAC;QAED,cAAa;QACb,iBAAiB,CAAC,MAAM,EAAE,aAAa;YACtC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,EAAA,gCAAA,CAAA,CAAA;YAC9B,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,EAAA,gCAAA,CAAA,CAAA;YAC1C,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,EAAA,gCAAA,CAAA,CAAA;QACvC,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;;;WAjRA,GAAA,CA0Bc,aAAA,EAAA,GAAA,CAAA,EA1BA,KAAK,EAAC,SAAS,EAAA,CAAA,EAAA;QAC5B,GAAA,CAA8C,QAAA,EAAA,GAAA,CAAA,EAArC,OAAK,EAAE,IAAA,CAAA,eAAe,EAAA,CAAA,EAAE,MAAI,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;QACrC,GAAA,CAAqC,QAAA,EAAA,GAAA,CAAA,EAA5B,OAAK,EAAE,IAAA,CAAA,OAAO,EAAA,CAAA,EAAE,KAAG,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;QAC5B,GAAA,CAAsD,QAAA,EAAA,GAAA,CAAA,EAA7C,OAAK,EAAE,IAAA,CAAA,kBAAkB,EAAA,CAAA,EAAE,WAAS,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;QAC7C,GAAA,CAAiG,4BAAA,EAAA,GAAA,CAAA;YAA9E,GAAG,EAAC,aAAa;YAAE,QAAM,EAAE,IAAA,CAAA,QAAQ;YAAG,SAAO,EAAE,IAAA,CAAA,SAAS;;QAC3E,GAAA,CAGyB,8BAAA,EAAA,GAAA,CAAA,EAFtB,GAAG,EAAC,iBAAiB,EAAA,CAAA,EAAA,IAAA,EAAA,GAAA,CAAA,gBAAA,CAAA;QAGxB,GAAA,CAQwB,+BAAA,EAAA,GAAA,CAAA;YAPvB,GAAG,EAAC,gBAAgB;YACnB,IAAI,EAAE,IAAA,CAAA,YAAY;YAClB,KAAK,EAAE,IAAA,CAAA,aAAa;YACpB,WAAW,EAAE,IAAA,CAAA,WAAW;YACxB,YAAY,EAAE,IAAA,CAAA,YAAY;YAC1B,QAAM,EAAE,IAAA,CAAA,gBAAgB;YACxB,SAAO,EAAE,IAAA,CAAA,iBAAiB;;QAG5B,GAAA,CAKG,oBAAA,EAAA,GAAA,CAAA;YAJA,QAAQ,EAAE,IAAA,CAAA,UAAU;YACrB,KAAK,EAAC,QAAQ;YACd,OAAO,EAAC,WAAW;YACnB,GAAG,EAAC,UAAU;;QAEhB,GAAA,CAAwC,QAAA,EAAA,GAAA,CAAA,EAA/B,OAAK,EAAE,IAAA,CAAA,QAAQ,EAAA,CAAA,EAAE,OAAK,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA", "file": "pages/index/index.uvue", "sourcesContent": ["<template>\r\n\t<scroll-view  class=\"content\">\r\n\t\t<button @click=\"openColorPicker\">选择颜色</button>\r\n\t\t<button @click=\"openFun\">对话框</button>\r\n\t\t<button @click=\"openDateTimePicker\">测试日期时间选择器</button>\r\n\t\t<main-color-picker ref=\"colorPicker\" @cancel=\"onCancel\" @confirm=\"onConfirm\"></main-color-picker>\r\n\t\t<mainYearmonthPicker\r\n\t\t\t\t\tref=\"yearmonthPicker\"\r\n\r\n\t\t\t\t></mainYearmonthPicker>\r\n\t\t<main-datetime-picker\r\n\t\t\tref=\"datetimePicker\"\r\n\t\t\t:mode=\"dateTimeMode\"\r\n\t\t\t:title=\"dateTimeTitle\"\r\n\t\t\t:showSeconds=\"showSeconds\"\r\n\t\t\t:quickOptions=\"quickOptions\"\r\n\t\t\t@cancel=\"onDateTimeCancel\"\r\n\t\t\t@confirm=\"onDateTimeConfirm\"\r\n\t\t></main-datetime-picker>\r\n\t\t\r\n\t\t<main-form \r\n\t\t\t\t:formData=\"formConfig\"\r\n\t\t\t\ttitle=\"用户信息表单\"\r\n\t\t\t\tkeyName=\"user_form\"\r\n\t\t\t\tref=\"mainForm\"\r\n\t\t\t/>\r\n\t\t<button @click=\"viewForm\">查看表单1</button>\r\n\t</scroll-view>\r\n</template>\r\n\r\n<script>\r\n\timport { FormFieldData, DateQuickOption } from '@/components/main-form/form_type.uts'\r\n\timport mainYearmonthPicker from '@/components/main-form/tools/main-yearmonth-picker.uvue'\r\n\timport mainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'\r\n\r\n\texport default {\r\n\t\tcomponents:{\r\n\t\t\tmainYearmonthPicker,\r\n\t\t\tmainDatetimePicker\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\t// 日期时间选择器配置\r\n\t\t\t\tdateTimeMode: 'time' as string,\r\n\t\t\t\tdateTimeTitle: '选择日期时间' as string,\r\n\t\t\t\tshowSeconds: false as boolean,\r\n\t\t\t\tquickOptions: [] as DateQuickOption[],\r\n\t\t\t    formConfig: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"username\",\r\n\t\t\t\t\t\tname: \"用户名1\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 0,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入用户名\",\r\n\t\t\t\t\t\t\ttip:\"123\",\r\n\t\t\t\t\t\t\tinputmode: \"digit\" \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"password\",\r\n\t\t\t\t\t\tname: \"密码\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:false,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"密码请自己保管好\",\r\n\t\t\t\t\t\t\tinputmode: \"number\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"email\",\r\n\t\t\t\t\t\tname: \"邮箱地址\",\r\n\t\t\t\t\t\ttype: \"textarea\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"enable_feature\",\r\n\t\t\t\t\t    name: \"启用功能\",\r\n\t\t\t\t\t    type: \"switch\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: { \r\n\t\t\t\t\t        \"varType\": \"number\",\r\n\t\t\t\t\t        \"tip\": \"开启后将启用此功能\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"slider\",\r\n\t\t\t\t\t    name: \"slider测试\",\r\n\t\t\t\t\t    type: \"slider\",\r\n\t\t\t\t\t    value: 10,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 0,\r\n\t\t\t\t\t        \"max\": 100,\r\n\t\t\t\t\t\t\t\"step\":1\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"numberbox\",\r\n\t\t\t\t\t    name: \"数量选择\",\r\n\t\t\t\t\t    type: \"numberbox\",\r\n\t\t\t\t\t    value: 5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 1,\r\n\t\t\t\t\t        \"max\": 50,\r\n\t\t\t\t\t\t\t\"step\": 1,\r\n\t\t\t\t\t\t\t\"unit\": \"个\",\r\n\t\t\t\t\t\t\t\"tip\": \"请选择数量\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"themeColor\",\r\n\t\t\t\t\t    name: \"主题颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"hex\",\r\n\t\t\t\t\t        \"tip\": \"选择您喜欢的主题颜色\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"backgroundColor\",\r\n\t\t\t\t\t    name: \"背景颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"rgba(255, 0, 0, 0.8)\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"rgba\",\r\n\t\t\t\t\t        \"tip\": \"选择背景颜色，支持透明度\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"birthYearMonth\",\r\n\t\t\t\t\t    name: \"出生年月\",\r\n\t\t\t\t\t    type: \"yearmonth\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"tip\": \"请选择您的出生年月1\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"city\",\r\n\t\t\t\t\t    name: \"所在城市\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"string\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择城市\",\r\n\t\t\t\t\t        \"tip\": \"选择您所在的城市\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"北京\", \"value\": \"beijing\"},\r\n\t\t\t\t\t            {\"text\": \"上海\", \"value\": \"shanghai\"},\r\n\t\t\t\t\t            {\"text\": \"广州\", \"value\": \"guangzhou\"},\r\n\t\t\t\t\t            {\"text\": \"深圳\", \"value\": \"shenzhen\"},\r\n\t\t\t\t\t            {\"text\": \"杭州\", \"value\": \"hangzhou\"}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"level\",\r\n\t\t\t\t\t    name: \"用户等级\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"int\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择等级\",\r\n\t\t\t\t\t        \"tip\": \"选择您的用户等级\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"初级用户\", \"value\": 1},\r\n\t\t\t\t\t            {\"text\": \"中级用户\", \"value\": 2},\r\n\t\t\t\t\t            {\"text\": \"高级用户\", \"value\": 3},\r\n\t\t\t\t\t            {\"text\": \"VIP用户\", \"value\": 4}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"score\",\r\n\t\t\t\t\t    name: \"评分\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 4.5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"float\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择评分\",\r\n\t\t\t\t\t        \"tip\": \"选择您的评分\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"1.0分\", \"value\": 1.0},\r\n\t\t\t\t\t            {\"text\": \"2.5分\", \"value\": 2.5},\r\n\t\t\t\t\t            {\"text\": \"3.0分\", \"value\": 3.0},\r\n\t\t\t\t\t            {\"text\": \"4.5分\", \"value\": 4.5},\r\n\t\t\t\t\t            {\"text\": \"5.0分\", \"value\": 5.0}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\r\n\t\t\t\t] as FormFieldData[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tviewForm(){\r\n\t\t\t\t// this.formConfig[0].value=\"111\"\r\n\t\t\t\t// console.log(this.formConfig)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/calendar-test/calendar-test\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\topenFun() {\r\n\t\t\t\t(this.$refs['yearmonthPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t\r\n\t\t\t\t// uni.showModal({\r\n\t\t\t\t// \ttitle: \"onLoad 调用示例,请手动取消\",\r\n\t\t\t\t// \teditable: true,\r\n\t\t\t\t// \tcontent: \"Hello World\",\r\n\t\t\t\t// \tsuccess: function (res) {\r\n\t\t\t\t// \t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\tconsole.log('用户点击确定')\r\n\t\t\t\t// \t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\tconsole.log('用户点击取消')\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t\topenColorPicker() {\r\n\t\t\t\t// 使用$callMethod方式调用组件方法\r\n\t\t\t\t(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t},\r\n\r\n\t\t\tonCancel() {\r\n\t\t\t\tconsole.log('用户取消选择')\r\n\t\t\t},\r\n\r\n\t\t\tonConfirm(result : UTSJSONObject) {\r\n\t\t\t\tconsole.log(result)\r\n\t\t\t\tconsole.log('选择的颜色:', result['color'])\r\n\t\t\t\tconsole.log('RGBA值:', result['rgba'])\r\n\t\t\t},\r\n\r\n\t\t\t// 打开日期时间选择器\r\n\t\t\topenDateTimePicker() {\r\n\t\t\t\t(this.$refs['datetimePicker'] as ComponentPublicInstance).$callMethod('show')\r\n\t\t\t},\r\n\r\n\t\t\t// 日期时间选择器取消事件\r\n\t\t\tonDateTimeCancel() {\r\n\t\t\t\tconsole.log('用户取消选择日期时间')\r\n\t\t\t},\r\n\r\n\t\t\t// 日期时间选择器确认事件\r\n\t\t\tonDateTimeConfirm(result: UTSJSONObject) {\r\n\t\t\t\tconsole.log('选择的日期时间:', result)\r\n\t\t\t\tconsole.log('格式化后的值:', result['formatted'])\r\n\t\t\t\tconsole.log('Date对象:', result['value'])\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.content {\r\n\t\theight: 100%;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n</style>"]}